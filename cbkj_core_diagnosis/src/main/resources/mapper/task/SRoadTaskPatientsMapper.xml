<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cbkj.diagnosis.mapper.task.SRoadTaskPatientsMapper">

    <resultMap id="BaseResultMap" type="com.cbkj.diagnosis.beans.task.SRoadTaskPatients">
        <id column="task_patients_id" jdbcType="BIGINT" property="taskPatientsId"/>
        <result column="road_execute_event" jdbcType="VARCHAR" property="roadExecuteEvent"/>
        <result column="road_execute_event_time" jdbcType="VARCHAR" property="roadExecuteEventTime"/>
        <result column="road_execute_event_unit" jdbcType="VARCHAR" property="roadExecuteEventUnit"/>
        <result column="road_execute_event_way" jdbcType="VARCHAR" property="roadExecuteEventWay"/>
        <result column="road_execute_event_type" jdbcType="VARCHAR" property="roadExecuteEventType"/>
        <result column="road_execute_event_content_id" jdbcType="VARCHAR" property="roadExecuteEventContentId"/>
        <result column="road_execute_event_content_name" jdbcType="VARCHAR" property="roadExecuteEventContentName"/>
        <result column="s_road_id" jdbcType="VARCHAR" property="sRoadId"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="s_road_task_id" jdbcType="VARCHAR" property="sRoadTaskId"/>
        <result column="patient_id" jdbcType="VARCHAR" property="patientId"/>
        <result column="patient_name" jdbcType="VARCHAR" property="patientName"/>
        <result column="patient_age" jdbcType="VARCHAR" property="patientAge"/>
        <result column="patient_sex" jdbcType="VARCHAR" property="patientSex"/>
        <result column="patient_card_number" jdbcType="VARCHAR" property="patientCardNumber"/>
        <result column="task_excute_time" jdbcType="TIMESTAMP" property="taskExcuteTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="task_excute_status" jdbcType="INTEGER" property="taskExcuteStatus"/>
        <result column="records_id" jdbcType="VARCHAR" property="recordsId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="hand_send" jdbcType="VARCHAR" property="handSend"/>
        <result column="rec_id" jdbcType="VARCHAR" property="recId"/>
        <result column="road_execute_id" jdbcType="INTEGER" property="roadExecuteId"/>
        <result column="allot_time" jdbcType="TIMESTAMP" property="allotTime"/>
        <result column="allot_task_status" jdbcType="VARCHAR" property="allotTaskStatus"/>
        <result column="doctor_id" jdbcType="VARCHAR" property="doctorId"/>
        <result column="doctor_name" jdbcType="VARCHAR" property="doctorName"/>

        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="ins_code" jdbcType="VARCHAR" property="insCode"/>
        <result column="ins_id" jdbcType="VARCHAR" property="insId"/>
        <result column="ins_name" jdbcType="VARCHAR" property="insName"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="dept_code" jdbcType="VARCHAR" property="deptCode"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="task_patients_node" jdbcType="VARCHAR" property="taskPatientsNode"/>
        <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime"/>
        <result column="delete_user_id" jdbcType="VARCHAR" property="deleteUserId"/>
        <result column="delete_user_name" jdbcType="VARCHAR" property="deleteUserName"/>


    </resultMap>


    <sql id="Base_Column_List">
        task_patients_id,road_execute_event,road_execute_event_time,road_execute_event_unit,road_execute_event_way,
        road_execute_event_type,road_execute_event_content_id,s_road_id,s_road_task_id,patient_id,
        patient_name,patient_age,patient_sex,patient_card_number,task_excute_time,task_excute_status,
        records_id,status,task_name,road_execute_event_content_name,hand_send,rec_id,road_execute_id,allot_time,allot_task_status,
            doctor_id,doctor_name,
            app_id,ins_code,ins_id,ins_name,dept_id,dept_code,dept_name,create_time
    </sql>

    <delete id="deleteByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.task.SRoadTaskPatients">
        delete
        from s_road_task_patients
        where task_patients_id = #{ taskPatientsId }
    </delete>

    <delete id="deleteBylist" parameterType="ArrayList">
        delete from s_road_task_patients where task_patients_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <update id="deleteStatusByRoadTaskId">

        update s_road_task_patients
        set status = '1',task_excute_status = '3'
        where s_road_task_id = #{sRoadTaskId}
          and status = '0'
         <!-- and task_excute_status in ('1') -->
          and task_excute_status in(1,2)
          and task_excute_time >  (CONCAT(CURDATE(), ' 23:59:59') )
    </update>
    <update id="deletePhoneStatusByRoadTaskId">

        update s_road_task_patients_phone
        set phone_status = '3'
        where s_road_task_id = #{sRoadTaskId}
          and phone_status in(1,2)
          and sui_fang_time >  (CONCAT(CURDATE(), ' 23:59:59') )
    </update>
    <update id="cancelStatusByRoadTaskId">
        update s_road_task_patients
        set task_excute_status = '3'
        where s_road_task_id = #{sRoadTaskId}
          and status = '0'
          and task_excute_status in ('1')
    </update>

    <delete id="deleteTaskPatientIdKey" parameterType="Long">
        delete
        from s_road_task_patients
        where task_patients_id = #{ taskPatientsId }
    </delete>

    <insert id="insert" parameterType="com.cbkj.diagnosis.beans.task.SRoadTaskPatients" useGeneratedKeys="true" keyColumn="task_patients_id" keyProperty="taskPatientsId">
        insert into s_road_task_patients (<include refid="Base_Column_List"/>) values
        (#{taskPatientsId},#{roadExecuteEvent},#{roadExecuteEventTime},#{roadExecuteEventUnit},#{roadExecuteEventWay},#{roadExecuteEventType},#{roadExecuteEventContentId},
        #{sRoadId},#{sRoadTaskId},#{patientId},#{patientName},#{patientAge},#{patientSex},
        #{patientCardNumber},#{taskExcuteTime},#{taskExcuteStatus},#{recordsId},#{status},
         #{taskName},#{roadExecuteEventContentName},#{handSend},#{recId},#{roadExecuteId},#{allotTime},#{allotTaskStatus},#{doctorId},#{doctorName},
         #{appId},#{insCode},#{insId},#{insName},#{deptId},#{deptCode},#{deptName},now())
    </insert>

    <insert id="insertList" parameterType="List" useGeneratedKeys="true" keyColumn="task_patients_id" keyProperty="taskPatientsId">
        insert into s_road_task_patients (<include refid="Base_Column_List"/>) values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.taskPatientsId},#{item.roadExecuteEvent},#{item.roadExecuteEventTime},#{item.roadExecuteEventUnit},#{item.roadExecuteEventWay},#{item.roadExecuteEventType},
            #{item.roadExecuteEventContentId},#{item.sRoadId},#{item.sRoadTaskId},#{item.patientId},#{item.patientName},#{item.patientAge},#{item.patientSex},
            #{item.patientCardNumber},#{item.taskExcuteTime},#{item.taskExcuteStatus},#{item.recordsId},#{item.status},
            #{item.taskName},#{item.roadExecuteEventContentName},#{item.handSend},#{item.recId},#{item.roadExecuteId},#{item.allotTime},#{item.allotTaskStatus}
            ,#{item.doctorId},#{item.doctorName},
             #{item.appId},#{item.insCode},#{item.insId},#{item.insName},#{item.deptId},#{item.deptCode},#{item.deptName},now())
        </foreach>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.task.SRoadTaskPatients">
        update s_road_task_patients
        <set>
            <if test="roadExecuteEvent != null">
                road_execute_event = #{ roadExecuteEvent },
            </if>
            <if test="roadExecuteEventTime != null">
                road_execute_event_time = #{ roadExecuteEventTime },
            </if>
            <if test="roadExecuteEventUnit != null">
                road_execute_event_unit = #{ roadExecuteEventUnit },
            </if>
            <if test="roadExecuteEventWay != null">
                road_execute_event_way = #{ roadExecuteEventWay },
            </if>
            <if test="roadExecuteEventType != null">
                road_execute_event_type = #{ roadExecuteEventType },
            </if>
            <if test="roadExecuteEventContentId != null">
                road_execute_event_content_id = #{ roadExecuteEventContentId },
            </if>
            <if test="sRoadId != null">
                s_road_id = #{ sRoadId },
            </if>
            <if test="sRoadTaskId != null">
                s_road_task_id = #{ sRoadTaskId },
            </if>
            <if test="patientId != null">
                patient_id = #{ patientId },
            </if>
            <if test="patientName != null">
                patient_name = #{ patientName },
            </if>
            <if test="patientAge != null">
                patient_age = #{ patientAge },
            </if>
            <if test="patientSex != null">
                patient_sex = #{ patientSex },
            </if>
            <if test="patientCardNumber != null">
                patient_card_number = #{ patientCardNumber },
            </if>
            <if test="taskExcuteTime != null">
                task_excute_time = #{ taskExcuteTime },
            </if>
            <if test="taskExcuteStatus != null">
                task_excute_status = #{ taskExcuteStatus },
            </if>

            <if test="status != null">
                status = #{status},
            </if>
            <if test="taskName != null">
                task_name = #{taskName},
            </if>
            <if test="handSend != null">
                hand_send = #{handSend},
            </if>
            <if test="recId != null">
                rec_id = #{recId},
            </if>
            <if test="roadExecuteEventContentName != null">
                road_execute_event_content_name = #{roadExecuteEventContentName},
            </if>
            <if test="roadExecuteId != null">
                road_execute_id = #{roadExecuteId},
            </if>
            <if test="allotTime != null">
                allot_time = #{allotTime},
            </if>
            <if test="allotTaskStatus != null">
                allot_task_status = #{allotTaskStatus},
            </if>
            <if test="doctorId != null and doctorId != ''">
                doctor_id = #{doctorId},
            </if>
            <if test="doctorName != null and doctorName != ''">
                doctor_name = #{doctorName},
            </if>
            <if test="taskPatientsNode != null and taskPatientsNode != ''">
                task_patients_node = #{taskPatientsNode},
            </if>
            <if test="deleteTime != null ">
                delete_time = #{deleteTime},
            </if>
            <if test="deleteUserId != null and deleteUserId != ''">
                delete_user_id = #{deleteUserId},
            </if>
            <if test="deleteUserName != null and deleteUserName != ''">
                delete_user_name = #{deleteUserName},
            </if>
        </set>
        where task_patients_id = #{ taskPatientsId }
    </update>
    <update id="updatePatientTaskStatusByPAndR" parameterType="com.cbkj.diagnosis.service.webapi.business.vo.PatientTaskDelete">
        update s_road_task_patients
        <set>

            task_excute_status = #{taskPatientStatus}

        </set>
        where s_road_task_id = #{sRoadTaskId} and patient_id = #{patientId} and task_excute_status = #{taskPatientStatusPast}
    </update>
    <update id="updateTaskExcuteStatusByPrimaryKey" parameterType="String">
        update s_road_task_patients
        <set>

            task_excute_status = 7

        </set>
        where task_patients_id = #{taskPatientsId} and task_excute_status = 2
    </update>
    <update id="updateRecIdByPrimaryKey" parameterType="com.cbkj.diagnosis.beans.task.SRoadTaskPatients">
        update s_road_task_patients
        <set>

            rec_id = #{recId},
            task_excute_status = #{taskExcuteStatus}

        </set>
        where task_patients_id = #{taskPatientsId}
    </update>
    <update id="upDateSchedulerTaskStatusById" parameterType="String">


        update s_road_task_patients
        <set>


            task_excute_status = 2

        </set>
        where task_patients_id = #{nextId} and task_excute_status = 1
    </update>
    <update id="updateReadStatusByPrimaryKey" parameterType="Integer">
        update s_road_task_patients
        <set>
            task_excute_status = 7
        </set>
        where task_patients_id = #{taskPatientsId} and task_excute_status = 2
    </update>

    <select id="getObjectById" resultMap="BaseResultMap" parameterType="String">
        select
        <include refid="Base_Column_List"/>
        from s_road_task_patients where task_patients_id = #{ taskPatientsId }
    </select>

    <select id="getPageListByObj" parameterType="com.cbkj.diagnosis.beans.task.SRoadTaskPatients"
            resultMap="BaseResultMap">
        SELECT
        task_patients_id,road_execute_event,road_execute_event_time,road_execute_event_unit,road_execute_event_way,
        road_execute_event_type,road_execute_event_content_id,s_road_id,s_road_task_id,patient_id,patient_name,patient_age,patient_sex,patient_card_number,
        task_excute_time,task_excute_status,records_id,status,task_name,road_execute_event_content_name,rec_id
        from s_road_task_patients,hand_send,road_execute_id,allot_time,allot_task_status,doctor_id,doctor_name,dept_name
        <where>
            status = '0'
            <if test=" name != null and name!='' ">
                and name like CONCAT('%',trim(#{name}),'%')
            </if>
        </where>
    </select>
    <select id="getMobileIndexNumber" resultType="java.lang.Integer"
            parameterType="com.cbkj.diagnosis.service.mobileapi.vo.GetMobileIndexNumber">

        SELECT COUNT(*)
        FROM `s_road_task_patients`
        WHERE road_execute_event_type = #{roadExecuteEventType}
          AND patient_id = #{patientId}
          AND STATUS = '0'
          AND task_excute_status IN ('2')


    </select>

    <select id="getMobileIndexNumber2" resultType="java.lang.Integer"
            parameterType="com.cbkj.diagnosis.service.mobileapi.vo.GetMobileIndexNumber">

        SELECT COUNT(*)
        FROM `s_road_task_patients`
        WHERE road_execute_event_type in ('2', '4') and road_execute_event_way = '1'
          AND patient_id = #{patientId}
          AND STATUS = '0'
          AND task_excute_status =2


    </select>


    <resultMap id="BaseResultMap2" type="com.cbkj.diagnosis.beans.task.SRoadTaskPatients">
        <id column="task_patients_id" jdbcType="BIGINT" property="taskPatientsId"/>
        <result column="task_excute_time" jdbcType="TIMESTAMP" property="taskExcuteTime"/>
        <result column="road_execute_event_content_id" jdbcType="VARCHAR" property="roadExecuteEventContentId"/>
        <result column="road_execute_event_content_name" jdbcType="VARCHAR" property="roadExecuteEventContentName"/>
    </resultMap>
    <resultMap id="BaseResultMap22" type="com.cbkj.diagnosis.service.mobileapi.vo.MessageListVo">
        <id column="task_patients_id" jdbcType="BIGINT" property="taskPatientsId"/>
        <result column="task_excute_status" jdbcType="INTEGER" property="taskExcuteStatus"/>
        <result column="road_execute_event_content_id" jdbcType="VARCHAR" property="roadExecuteEventContentId"/>
        <result column="road_execute_event_content_name" jdbcType="VARCHAR" property="roadExecuteEventContentName"/>
        <result column="task_excute_time" jdbcType="TIMESTAMP" property="taskExcuteTime"/>
        <result column="form_name" jdbcType="VARCHAR" property="formName"/>
        <result column="dia_type" jdbcType="VARCHAR" property="diaType"/>
        <result column="edu_abstract" jdbcType="VARCHAR" property="eduAbstract"/>
        <result column="insName" jdbcType="VARCHAR" property="insName"/>
    </resultMap>
    <select id="getMessageList" parameterType="com.cbkj.diagnosis.service.mobileapi.vo.GetFurtherConsultationList"
            resultMap="BaseResultMap22">
        SELECT
        a.task_patients_id,
        a.road_execute_event_content_id,
        a.road_execute_event_content_name,
        a.task_excute_time,
        CONCAT_WS('-',a.doctor_name,a.dept_name,a.ins_name) as insName,
            a.task_excute_status
        <if test="roadExecuteEventType == 1">
            ,edu.edu_abstract
        </if>
        FROM `s_road_task_patients` as a
        <if test="roadExecuteEventType == 1">
            join t_propaganda_edu as edu on(edu.t_propaganda_edu_id = a.road_execute_event_content_id)
        </if>
        WHERE a.STATUS = '0'
        AND a.patient_id= #{patientId}

        AND a.road_execute_event_type = #{roadExecuteEventType} AND a.task_excute_status IN ('2','7')


        order by a.task_excute_time desc
    </select>
    <select id="getSuiFangMessageList" parameterType="com.cbkj.diagnosis.service.mobileapi.vo.GetFurtherConsultationList"
            resultMap="BaseResultMap22">
        SELECT
        a.task_patients_id,
        a.road_execute_event_content_id,
        a.road_execute_event_content_name,
        a.task_excute_time,
        CONCAT_WS('-',a.doctor_name,a.dept_name,a.ins_name) as insName,
            task_excute_status,
        b.form_name,
        b.dia_type
        FROM `s_road_task_patients` as a

        join t_pre_diagnosis_form as b on(a.road_execute_event_content_id=b.dia_id)


        WHERE a.STATUS = '0'
        AND a.patient_id= #{patientId}

        AND a.road_execute_event_type in(2,4) AND a.task_excute_status in (2,7)

        and a.road_execute_event_way = '1'



        order by a.task_excute_time desc
    </select>

    <select id="getSuiFangMessageRecordList"
            parameterType="com.cbkj.diagnosis.service.mobileapi.vo.GetFurtherConsultationList"
            resultMap="BaseResultMap2">
        SELECT road_execute_event_content_id,
               road_execute_event_content_name,
               task_excute_time,
               (SELECT ins_name
                FROM `sys_ins`
                LIMIT 1) AS insName
        FROM `s_road_task_patients`
        WHERE STATUS = '0'
          AND patient_id = #{patientId}

          AND road_execute_event_type in ('2', '4')
          AND task_excute_status = '8'

    </select>
    <select id="getTaskPageList" resultMap="BaseResultMap"
            parameterType="com.cbkj.diagnosis.service.common.vo.AiSuiFangListRe">

        select
        a.task_patients_id,
        a.road_execute_event,
        a.road_execute_event_time,
        a.road_execute_event_unit,
        a.road_execute_event_way,
        a.road_execute_event_type,
        a.road_execute_event_content_id,
        a.s_road_id,
        a.s_road_task_id,
        a.patient_id,
        a.patient_name,
        a.patient_age,
        a.patient_sex,
        a.patient_card_number,
        a.task_excute_time,
        a.task_excute_status,
        a.records_id,
        a.task_name,
        a.road_execute_event_content_name,
        a.rec_id,
        a.road_execute_id,
        tai.MOBILE as patientMobile

        from s_road_task_patients as a
        <if test="diseaseName != null">
            join medical_records as b on(a.records_id = b.records_id and b.)
            and (b.chinese_dis_name like CONCAT('%',trim(#{diseaseName}),'%') or b.west_dis_name like
            CONCAT('%',trim(#{diseaseName}),'%'))
        </if>
        join t_admin_info as tai on(tai.USER_ID = a.patient_id )

        <where>
            a.status = '0' and a.hand_send = '0'
            and a.road_execute_event_way != '3'
            <if test="roadDetailEventWay != null">
                and a.road_execute_event_way=#{roadDetailEventWay}
            </if>
            <if test="sRoadTaskId != null">
                and a.s_road_task_id=#{sRoadTaskId}
            </if>

            <if test="roadExecuteEventContentId != null">
                and a.road_execute_event_content_id=#{roadExecuteEventContentId}
            </if>

            <if test="roadDetailEventType != null">
                and a.road_execute_event_type=#{roadDetailEventType}
            </if>

            <if test="taskExcuteStatus != null">
                and a.task_excute_status=#{taskExcuteStatus}
            </if>
            <if test="patientIdcard != null">
                and a.patient_card_number=#{patientIdcard}
            </if>

            <if test="patientName != null">
                and a.patient_name=#{patientName}
            </if>
            <if test="startDate != null">
                and a.task_excute_time>=#{startDate}
            </if>
            <if test="endDate != null">
                and a.task_excute_time &lt;= #{endDate}
            </if>


        </where>
    </select>


    <resultMap id="BaseResultMap3" type="com.cbkj.diagnosis.beans.TAdminInfo">
        <id column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="last_chinese_dis_name" jdbcType="VARCHAR" property="lastChineseDisName"/>
        <result column="records_id" jdbcType="VARCHAR" property="recordsId"/>
        <result column="USER_NAME" jdbcType="VARCHAR" property="userName"/>
        <result column="last_west_dis_name" jdbcType="VARCHAR" property="lastWestDisName"/>
        <result column="last_doctor_name" jdbcType="VARCHAR" property="lastDoctorName"/>
        <result column="last_sym_name" jdbcType="VARCHAR" property="lastSymName"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="card_number" jdbcType="VARCHAR" property="cardNumber"/>
        <result column="age" jdbcType="VARCHAR" property="age"/>
        <result column="sex" jdbcType="VARCHAR" property="sex"/>
        <result column="join_road_task" jdbcType="VARCHAR" property="joinRoadTask"/>
        <result column="taskPatientStatus" jdbcType="VARCHAR" property="taskPatientStatus"/>
        <result column="last_records_time" jdbcType="TIMESTAMP" property="lastRecordsTime"/>
        <result column="records_id" jdbcType="VARCHAR" property="recordsId"/>
        <result column="hasDiagnosis" jdbcType="INTEGER" property="hasDiagnosis"/>

        <result column="last_west_dis_name" jdbcType="VARCHAR" property="lastWestDisName"/>
        <result column="last_chinese_dis_name" jdbcType="VARCHAR" property="lastChineseDisName"/>
        <result column="last_doctor_name" jdbcType="VARCHAR" property="lastDoctorName"/>
        <result column="last_doctor_id" jdbcType="VARCHAR" property="lastDoctorId"/>
        <result column="last_records_id" jdbcType="VARCHAR" property="lastRecordsId"/>
        <result column="last_records_time" jdbcType="TIMESTAMP" property="lastRecordsTime"/>

    </resultMap>
    <select id="getPatientList" parameterType="com.cbkj.diagnosis.service.webapi.business.vo.GetPatientListVo"
            resultMap="BaseResultMap3">
        select
            distinct
        t.user_id,t.USER_NAME,t.card_number,t.AGE,t.SEX,t.last_chinese_dis_name,
        t.last_west_dis_name,t.last_doctor_name,t.last_doctor_id,t.last_sym_name,t.remark,t.join_road_task,t.last_records_time



        <if test="sRoadTaskId != null and sRoadTaskId != '' ">
            ,sm.status as taskPatientStatus,sm.records_id,
            ( select count(*) from t_record as tr where tr.patient_id=t.user_id) > 0 as hasDiagnosis
        </if>

        from t_admin_info as t
        <if test="sRoadTaskId != null and sRoadTaskId != '' ">
                join  s_road_task_patients_mapping as sm on(sm.s_road_task_id = #{sRoadTaskId} and sm.patient_id = t.user_id)
        </if>

        <where>
            <if test="patientCardNumber != null">
                and t.card_number=#{patientCardNumber}
            </if>
            <if test="patientName != null">
                and t.USER_NAME like CONCAT('%',trim(#{patientName}),'%')
            </if>
            <if test="diseaseName != null and diseaseName != '' ">
                and
                (
                t.last_west_dis_name like CONCAT('%',trim(#{diseaseName}),'%')
                or
                t.last_chinese_dis_name like CONCAT('%',trim(#{diseaseName}),'%')
                )
            </if>
        <if test="startDate != null and startDate != ''">
            and t.last_records_time >= #{startDate}
        </if>
            <if test="endDate != null and endDate != ''">
                and t.last_records_time &lt;= #{endDate}
            </if>
            <if test="doctorName != null and doctorName != '' ">
                and t.last_doctor_name like CONCAT('%',trim(#{doctorName}),'%')
            </if>
            <if test="depName != null and depName != ''">
                and t.last_dept_name like CONCAT('%',trim(#{depName}),'%')
            </if>
            <if test="joinRoadTask != null and joinRoadTask != -1 ">
                and t.join_road_task =#{joinRoadTask}
            </if>
        </where>

    </select>







    <select id="getPatientListTask" parameterType="com.cbkj.diagnosis.service.webapi.business.vo.GetPatientListVo"
            resultMap="BaseResultMap3">
        select * from (

        select
        distinct
        t.user_id,t.USER_NAME,t.card_number,t.AGE,t.SEX,t.last_chinese_dis_name,
        t.last_west_dis_name,t.last_doctor_name,t.last_doctor_id,t.last_sym_name,t.remark,t.join_road_task
                                ,mr.record_time as last_records_time,


        sm.status as taskPatientStatus,sm.records_id,
        ( select count(*) from t_record as tr where tr.patient_id=t.user_id) > 0 as hasDiagnosis


        from t_admin_info as t

        join s_road_task_patients_mapping as sm on(sm.s_road_task_id = #{sRoadTaskId} and sm.patient_id = t.user_id)

         join medical_records as mr on(mr.records_id = sm.records_id)


        <where>
            <if test="patientCardNumber != null">
                and t.card_number=#{patientCardNumber}
            </if>
            <if test="patientName != null">
                and t.USER_NAME like CONCAT('%',trim(#{patientName}),'%')
            </if>
            <if test="diseaseName != null and diseaseName != '' ">
                and
                (
                t.last_west_dis_name like CONCAT('%',trim(#{diseaseName}),'%')
                or
                t.last_chinese_dis_name like CONCAT('%',trim(#{diseaseName}),'%')
                )
            </if>
            <if test="startDate != null and startDate != ''">
                and t.last_records_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                and t.last_records_time &lt;= #{endDate}
            </if>
            <if test="doctorName != null and doctorName != '' ">
                and t.last_doctor_name like CONCAT('%',trim(#{doctorName}),'%')
            </if>
            <if test="depName != null and depName != ''">
                and t.last_dept_name like CONCAT('%',trim(#{depName}),'%')
            </if>
            <if test="joinRoadTask != null and joinRoadTask != -1 ">
                and t.join_road_task =#{joinRoadTask}
            </if>
        </where>

        ) as a

        <where>
            <if test="hasDiagnosis != null and hasDiagnosis != -1">
                a.hasDiagnosis = #{hasDiagnosis}
            </if>
        </where>
order by a.last_records_time desc
    </select>



    <select id="getPatientFilterList" resultMap="BaseResultMap3"
            parameterType="com.cbkj.diagnosis.service.webapi.business.vo.GetPatientFilterListVo">
        select distinct
        t.user_id,t.USER_NAME,t.card_number,t.AGE,t.SEX,
        r.chinese_dis_name as last_chinese_dis_name,
        r.west_dis_name as last_west_dis_name,r.doctor_name as last_doctor_name,r.sym_name as last_sym_name,t.remark,r.records_id,r.record_time as last_records_time,
            (select  count(*) from t_record as tr  where tr.patient_id = t.USER_ID) > 0 as hasDiagnosis
        ,t.last_records_id
        from t_admin_info as t

        join medical_records as r on(r.patient_id = t.USER_ID)

        <where>
            <if test="patientCardNumber != null">
                and t.card_number=#{patientCardNumber}
            </if>
            <if test="patientName != null">
                and t.USER_NAME like CONCAT('%',trim(#{patientName}),'%')
            </if>
            <if test="doctorName != null">
                and r.doctor_name like CONCAT('%',trim(#{doctorName}),'%')
            </if>
            <if test="diseaseName != null and diseaseName != '' ">

                and
                (
                r.chinese_dis_name like CONCAT('%',trim(#{diseaseName}),'%')
                or
                r.west_dis_name like CONCAT('%',trim(#{diseaseName}),'%')
                )

            </if>
            <if test="startDate != null">
                and r.record_time>=#{startDate}
            </if>
            <if test="endDate != null">
                and r.record_time &lt;= #{endDate}
            </if>
            <if test="deptName != null">
                and r.dept_name like  CONCAT('%',trim(#{deptName}),'%')
            </if>

        </where>
order by t.last_records_time desc
    </select>

    <select id="getPatientFilterList2" resultMap="BaseResultMap3"
            parameterType="com.cbkj.diagnosis.service.webapi.business.vo.GetPatientFilterListVo">
        select distinct
        t.user_id,t.USER_NAME,t.card_number,t.AGE,t.SEX,
        r.chinese_dis_name as last_chinese_dis_name,
        r.west_dis_name as last_west_dis_name,r.doctor_name as last_doctor_name,r.sym_name as last_sym_name,t.remark,r.records_id,r.record_time as last_records_time,
        (select  count(*) from t_record as tr  where tr.patient_id = t.USER_ID) > 0 as hasDiagnosis
,t.last_records_id
        from t_admin_info as t
            join medical_records as r on(r.patient_id = t.USER_ID)
        <where>
            <if test="patientCardNumber != null">
                and t.card_number=#{patientCardNumber}
            </if>
            <if test="patientName != null">
                and t.USER_NAME like CONCAT('%',trim(#{patientName}),'%')
            </if>


        </where>
order by t.last_records_time desc
    </select>




    <select id="searchScheduling" resultMap="BaseResultMap"
            parameterType="com.cbkj.diagnosis.service.webapi.business.vo.SearchSchedulingVo">
        select
        task_patients_id,road_execute_event,road_execute_event_time,road_execute_event_unit,road_execute_event_way,
        road_execute_event_type,road_execute_event_content_id,s_road_id,s_road_task_id,patient_id,
        patient_name,patient_age,patient_sex,patient_card_number,task_excute_time,task_excute_status,
        records_id,status,task_name,road_execute_event_content_name,hand_send,rec_id,road_execute_id,
        (select record_time from medical_records as a where a.records_id = srtp.records_id) as taskBasicTime
        from s_road_task_patients as srtp
        <where>
        and hand_send = '0'
            <if test="taskExcuteStartTime != null">

                and task_excute_time >= #{taskExcuteStartTime}
            </if>
            <if test="taskExcuteStartTime != null">

                and task_excute_time &lt;= #{taskExcuteEndTime}
            </if>
            <if test="roadExecuteEventWay != null and roadExecuteEventWay != ''">
                and road_execute_event_way=#{roadExecuteEventWay}
            </if>
            <if test="patientId != null and patientId != ''">
                and patient_id=#{patientId}
            </if>
            <if test="sRoadTaskId != null and sRoadTaskId != ''">
                and s_road_task_id=#{sRoadTaskId}
            </if>
        </where>
order by srtp.task_excute_time desc
    </select>

    <resultMap id="BaseResultMap4" type="com.cbkj.diagnosis.service.common.vo.GetPhoneList">
        <id column="taskPatientsId" jdbcType="BIGINT" property="taskPatientsId"/>
    </resultMap>

    <select id="getPhoneList" resultMap="BaseResultMap4" parameterType="com.cbkj.diagnosis.service.common.vo.SuiFangPhoneRe">

        SELECT
        a.task_patients_id taskPatientsId,
        pp.records_id recordsId,
        b.USER_ID patientId,
        b.USER_NAME patientName,
        b.AGE patientAge,
        b.sex patientSex,
        b.card_number patientCardNumber,

        a.doctor_id doctorId ,
        a.doctor_name doctorName,
        a.allot_time allotTime,
        pp.sui_fang_time suiFangTime,

        a.task_excute_time taskExcuteTime,
        pp.task_patients_node taskPatientsNode,
        pp.sui_fang_finish_time suiFangFinishTime,
        srt.task_name taskName,
        b.last_chinese_dis_name lastChineseDisName,
        b.last_west_dis_name lastWestDisName,
        ( SELECT COUNT(*) FROM `s_road_task_patients_phone` AS pp2 WHERE pp2.task_patients_id=pp.task_patients_id ) AS suiFangTotalNums,
        ( SELECT COUNT(*) FROM `s_road_task_patients_phone` AS pp2 WHERE pp2.task_patients_id=pp.task_patients_id
                                                                     and
        pp2.phone_status  = 2 ) AS suiFangOtherNums
        FROM

        (

        select tpp.task_patients_id,
        tpp.records_id,
        tpp.sui_fang_time,
        tpp.task_patients_node,
        tpp.sui_fang_finish_time from ( select task_patients_id,records_id,sui_fang_time,task_patients_node,sui_fang_finish_time from s_road_task_patients_phone

                                     <where>
                         <if test="allotTaskStatus != null ">
                             AND `allot_task_status` = #{allotTaskStatus}
                         </if>
                          <if test="startDate != null">

                              and sui_fang_finish_time >= #{startDate}
                          </if>
                          <if test="endDate != null">

                              and sui_fang_finish_time &lt;= #{endDate}
                          </if>
                         <if test="doctorId != null ">
                             AND `doctor_id` = #{doctorId}
                         </if>
                      </where>
                      order by sui_fang_finish_time desc
                      ) tpp GROUP BY
                                <!-- 按照 任务-路径 ，task_patients_id 就是任务下的路径-->
                                tpp.task_patients_id
                              <!--  tpp.records_id -->
                      ) as pp

        JOIN s_road_task_patients a
        ON (
        pp.task_patients_id = a.task_patients_id
        )
        JOIN `t_admin_info` AS b
        ON (a.patient_id = b.USER_ID)
        join s_road_task as srt on(srt.s_road_task_id=a.s_road_task_id)
        <where>


            <!--             a.road_execute_event_type = '2'  -->
            <!--           AND a.road_execute_event_way = '3'  -->
    <!--
    <if test="allotTaskStatus != null ">
                AND pp.`allot_task_status` = #{allotTaskStatus}
            </if>
-->
            <if test="sRoadTaskId != null ">
                AND a.`s_road_task_id` = #{sRoadTaskId}
            </if>

            <if test="taskExcuteStatus != null ">
                AND a.`task_excute_status` = #{taskExcuteStatus}
            </if>
            <if test="patientCardNumber != null and patientCardNumber != ''">
                AND b.`card_number` = #{patientCardNumber}
            </if>
            <if test="patientIdcard != null and patientIdcard != ''">
                AND b.`card_number` = #{patientIdcard}
            </if>

            <if test="patientName != null and patientName != ''">
                AND b.`USER_NAME` = #{patientName}
            </if>

            <if test="diseaseName != null and diseaseName != ''">
                AND (
                b.`last_west_dis_name` like CONCAT('%',trim(#{diseaseName}),'%')
                or
                b.`last_chinese_dis_name` like CONCAT('%',trim(#{diseaseName}),'%')
                )
            </if>
            <if test="doctorId != null ">
                AND a.`doctor_id` = #{doctorId}
            </if>


            <if test="waitSuiFangNums != null and waitSuiFangNums == 0">
                AND
                (SELECT
                COUNT(*)
                FROM
                `s_road_task_patients_phone` AS aa
                WHERE
                aa.`task_patients_id` = a.`task_patients_id`

                AND aa.phone_status not in (3,8)

                ) =0
            </if>
            <if test="waitSuiFangNums != null and waitSuiFangNums == 1">
                AND
                (SELECT
                COUNT(*)
                FROM
                `s_road_task_patients_phone` AS aa
                WHERE
                aa.`task_patients_id` = a.`task_patients_id`

                AND aa.phone_status not in (3,8)

                ) > 0
            </if>
        </where>
order by pp.sui_fang_finish_time  desc


    </select>

    <resultMap id="BaseResultMap5" type="com.cbkj.diagnosis.beans.business.SRoadTaskPatientsPhone">
        <id column="id" jdbcType="BIGINT"  property="sRoadTaskPatientsPhoneId" />
        <result column="task_patients_id" jdbcType="BIGINT" property="taskPatientsId" />
        <result column="doctor_id" jdbcType="VARCHAR" property="doctorId" />
        <result column="doctor_name" jdbcType="VARCHAR" property="doctorName" />
        <result column="allot_task_status" jdbcType="VARCHAR" property="allotTaskStatus" />
        <result column="sui_fang_time" jdbcType="TIMESTAMP" property="suiFangTime" />
        <result column="allot_time" jdbcType="TIMESTAMP" property="allotTime" />
        <result column="patient_id" jdbcType="VARCHAR" property="patientId" />
        <result column="phone_status" jdbcType="INTEGER" property="phoneStatus" />
    </resultMap>

    <select id="getSchedulingList" resultMap="BaseResultMap5" parameterType="com.cbkj.diagnosis.beans.business.SRoadTaskPatientsPhone">
        select *
        from s_road_task_patients_phone
        <where>
            <if test="patientId != null">
                and patient_id=#{patientId}
            </if>
            <if test="taskPatientsId != null">
                and task_patients_id=#{taskPatientsId}
            </if>
        <if test="sRoadTaskId != null">
            and s_road_task_id=#{sRoadTaskId}
        </if>
        <if test="roadExecuteId != null">
            and road_execute_id=#{roadExecuteId}
        </if>
        </where>
        order by sui_fang_time asc
    </select>
    <resultMap id="BaseResultMap6" type="com.cbkj.diagnosis.service.webapi.business.vo.PhoneTaskPageListReturnVo">
        <id column="sRoadTaskPatientsPhoneId" jdbcType="BIGINT"  property="sRoadTaskPatientsPhoneId" />
    </resultMap>
    <select id="getPhoneTaskPageList"
            resultMap="BaseResultMap6" parameterType="com.cbkj.diagnosis.service.webapi.business.vo.PhoneTaskPageListCore">
        select a.id as sRoadTaskPatientsPhoneId,
        c.card_number as patientCardNumber,
        c.USER_NAME patientName,
        c.SEX patientSex,
        c.age age,
        b.road_execute_event_content_name roadExecuteEventContentName,
        b.road_execute_event_content_id roadExecuteEventContentId,
        a.sui_fang_time suiFangTime,
        a.sui_fang_finish_time suiFangFinishTime,
        a.phone_status phoneStatus,
        a.patient_id patientId,
        a.task_patients_id taskPatientsId,
        a.records_id recordsId,
        a.doctor_id doctorId,
        a.rec_id recId,
        a.doctor_name doctorName,
        b.task_name taskName
        from s_road_task_patients_phone a
        join t_admin_info as c on (a.patient_id = c.user_id)
        join s_road_task_patients b on (a.task_patients_id = b.task_patients_id)
        <where>

            <if test="startSuiFangTime != null">
                and a.sui_fang_time >= #{startSuiFangTime}
            </if>
            <if test="endSuiFangTime != null">
                and a.sui_fang_time &lt;= #{endSuiFangTime}
            </if>
            <if test="taskPatientsId != null">
                and b.task_patients_id = #{taskPatientsId}
            </if>
            <if test="sRoadTaskId != null">
                and b.s_road_task_id = #{sRoadTaskId}
            </if>
            <if test="diaId != null and diaId != ''">
                and b.road_execute_event_content_id = #{diaId}
            </if>
            <if test="doctorId != null and doctorId != ''">
                and a.doctor_id = #{doctorId}
            </if>
            <if test="phoneStatus != null">
                and a.phone_status = #{phoneStatus}
            </if>
            <if test="diseaseName != null and diseaseName !=''">
                AND (
                c.`last_west_dis_name` like CONCAT('%',trim(#{diseaseName}),'%')
                or
                c.`last_chinese_dis_name` like CONCAT('%',trim(#{diseaseName}),'%')
                )
            </if>
            <if test="patientCardNumber != null and patientCardNumber != ''">
                and b.patient_card_number = #{patientCardNumber}
            </if>
            <if test="patientName != null and patientName != ''">
                and b.patient_name = #{patientName}
            </if>
        </where>
order by a.sui_fang_finish_time desc
    </select>
    <resultMap id="BaseResultMap7" type="com.cbkj.diagnosis.beans.business.CompletedQuestionnaireListRes">
        <result column="finishTime" jdbcType="TIMESTAMP" property="finishTime"/>
        <result column="sendTime" jdbcType="TIMESTAMP" property="sendTime"/>
        <result column="patientAge" jdbcType="VARCHAR" property="patientAge"/>
        <result column="rec_id" jdbcType="VARCHAR" property="recId"/>
    </resultMap>
    <select id="getCountNumCompletedQuestionnaireList"
            resultType="Integer"
            parameterType="com.cbkj.diagnosis.beans.business.CompletedQuestionnaireCoreRe">
        select count(1) from (
        ( SELECT
        b.rec_id
        FROM
        `t_record` b
        JOIN `t_pre_diagnosis_form` c ON(c.`dia_id`=b.`dia_id` and c.`form_type` = '2'
        <if test="createUserId != null and createUserId !='' ">
            and exists(
            select 1 from t_pre_diagnosis_dis_mapping as tpdm , sys_admin_info_dis_mapping as saidm
            where tpdm.dis_id = saidm.dis_id and saidm.user_id = #{createUserId} and c.dia_id = tpdm.dia_id
            ) and exists(
            select 1 from s_road_task_ex as srte where srte.diagnosis_doctor_id = #{createUserId}

            )
        </if>
        )
        <where>
            <if test="dateType != null and dateType == 1">

                <if test="startDate != null">

                    and b.`create_date` >= #{startDate}
                </if>
                <if test="endDate != null">

                    and b.`create_date` &lt;= #{endDate}
                </if>
            </if>
            <if test="formName != null">
                and c.`form_name` = #{formName}
            </if>
            <if test="patientName != null">
                and b.`patient_name` = #{patientName}
            </if>
            <if test="patientCardNum != null and patientCardNum != ''">
                and b.`patient_idcard` = #{patientCardNum}
            </if>
            <if test="diaId != null">
                and c.dia_id = #{diaId}
            </if>
            <if test="disName != null and disName !=''">
                and exists(
                select 1 from t_pre_diagnosis_dis_mapping as tpdm where tpdm.dis_name like CONCAT('%',trim(#{disName}),'%')
                and tpdm.dia_id = b.`dia_id`
                )
            </if>
        </where>
        )
        ) a
    </select>
    <select id="getCompletedQuestionnaireList"
            resultMap="BaseResultMap7"
            parameterType="com.cbkj.diagnosis.beans.business.CompletedQuestionnaireCoreRe">
select a.* from (
        ( SELECT
        b.`create_date` as finishTime,

        b.`patient_name` patientName,
        b.`patient_idcard` patientCardNum,
        b.`patient_sex` patientSex,
        b.`patient_age` patientAge,
        c.`form_name` formName,


        b.`patient_id` patientId,

        b.dia_id diaId,
        b.rec_id
        FROM
       `t_record` b
        JOIN `t_pre_diagnosis_form` c ON(c.`dia_id`=b.`dia_id` and c.`form_type` = '2'
        <if test="createUserId != null and createUserId !='' ">

            and exists(
            select 1 from t_pre_diagnosis_dis_mapping as tpdm , sys_admin_info_dis_mapping as saidm
            where tpdm.dis_id = saidm.dis_id and saidm.user_id = #{createUserId} and c.dia_id = tpdm.dia_id

            ) and exists(
                select 1 from s_road_task_ex as srte where srte.diagnosis_doctor_id = #{createUserId}

            )
        </if>

            )

        <where>

            <if test="dateType != null and dateType == 1">
                <if test="startDate != null">

                    and b.`create_date` >= #{startDate}
                </if>
                <if test="endDate != null">

                    and b.`create_date` &lt;= #{endDate}
                </if>
            </if>
            <if test="patientName != null">
                and b.`patient_name` = #{patientName}
            </if>
            <if test="patientCardNum != null and patientCardNum != ''">
                and b.`patient_idcard` = #{patientCardNum}
            </if>
            <if test="formName != null">
                and c.`form_name` = #{formName}
            </if>

            <if test="diaId != null">
                and c.dia_id = #{diaId}
            </if>

            <if test="disName != null and disName !=''">
        and exists(
                select 1 from t_pre_diagnosis_dis_mapping as tpdm where tpdm.dis_name like CONCAT('%',trim(#{disName}),'%')
and tpdm.dia_id = b.`dia_id`
                )
            </if>
        </where>

        )

        ) a
        ORDER BY a.`finishTime` DESC limit #{limit} offset #{page}
    </select>


    <resultMap id="CountCompletedQuestionnaireList" type="com.cbkj.diagnosis.beans.business.CompletedQuestionnaireCount">
        <id column="diaId" jdbcType="VARCHAR"  property="diaId" />
        <id column="num" jdbcType="INTEGER"  property="num" />
        <id column="formName" jdbcType="VARCHAR"  property="formName" />

    </resultMap>
    <select id="getCountCompletedQuestionnaireList"
            resultMap="CountCompletedQuestionnaireList"
            parameterType="com.cbkj.diagnosis.beans.business.CompletedQuestionnaireCoreRe">

        SELECT COUNT(*) num  ,a.dia_id diaId,a.form_name formName FROM (
        SELECT
        c.`dia_id`,c.form_name
        FROM
            `t_record` b
        JOIN `t_pre_diagnosis_form` c ON(c.`dia_id`=b.`dia_id` and c.`form_type` = '2')
        <where>

            <if test="createUserId != null and createUserId !='' ">

                and exists(
                select 1 from t_pre_diagnosis_dis_mapping as tpdm , sys_admin_info_dis_mapping as saidm
                where tpdm.dis_id = saidm.dis_id and saidm.user_id = #{createUserId} and c.dia_id = tpdm.dia_id

                )
            </if>
            <if test="dateType != null and dateType == 1">

                <if test="startDate != null">

                    and b.`create_date` >= #{startDate}
                </if>
                <if test="endDate != null">

                    and b.`create_date` &lt;= #{endDate}
                </if>


            </if>

            <if test="formName != null">
                and c.`form_name` = #{formName}
            </if>

            <if test="diaId != null">
                and c.dia_id = #{diaId}
            </if>



            <if test="patientName != null">
                and b.`patient_name` = #{patientName}
            </if>
            <if test="patientCardNum != null and patientCardNum != ''">
                and b.`patient_idcard` = #{patientCardNum}
            </if>
            <if test="disName != null and disName !=''">
                and exists(
                select 1 from t_pre_diagnosis_dis_mapping as tpdm where tpdm.dis_name like CONCAT('%',trim(#{disName}),'%')
                and tpdm.dia_id = b.`dia_id`
                )
            </if>
        </where>




        ) a
        GROUP BY a.dia_id;
    </select>
    <select id="getCountCompletedQuestionnaireList2"
            resultMap="CountCompletedQuestionnaireList"
            parameterType="com.cbkj.diagnosis.beans.business.CompletedQuestionnaireCoreRe">

        SELECT  COUNT(*) num,
        a.dia_id diaId,
        a.chinese_dis_name formName FROM (
        SELECT
        c.`dia_id`,
        c.chinese_dis_name
        FROM  `t_record` b
        JOIN `t_pre_diagnosis_form` c ON(c.`dia_id`=b.`dia_id`)
        <where>
        c.`form_type` = '1' AND c.`chinese_dis_name` like CONCAT('%',trim('乳痈'),'%')
            <if test="dateType != null and dateType == 1">

                <if test="startDate != null">

                    and b.`create_date` >= #{startDate}
                </if>
                <if test="endDate != null">

                    and b.`create_date` &lt;= #{endDate}
                </if>


            </if>

            <if test="formName != null">
                and c.`form_name` = #{formName}
            </if>

            <if test="diaId != null">
                and c.dia_id = #{diaId}
            </if>

            <if test="disName != null and disName !=''">
                AND (
                c.`chinese_dis_name` like CONCAT('%',trim(#{disName}),'%')
                or
                c.`chinese_dis_name` like CONCAT('%',trim(#{disName}),'%')
                )
            </if>
        </where>
        ) a
        GROUP BY a.dia_id;
    </select>



    <resultMap id="BaseResultMap8" type="com.cbkj.diagnosis.beans.business.CompletedQuestionnaireListExcel">
        <id property="recId" column="recId" jdbcType="VARCHAR"/>
        <result column="finishTime" jdbcType="TIMESTAMP" property="finishTime"/>
        <result column="sendTime" jdbcType="TIMESTAMP" property="sendTime"/>
        <result column="patientAge" jdbcType="VARCHAR" property="patientAge"/>
        <result column="patientName" jdbcType="VARCHAR" property="patientName"/>
        <result column="patientCardNum" jdbcType="VARCHAR" property="patientCardNum"/>
        <result column="patientSex" jdbcType="VARCHAR" property="patientSex"/>
        <result column="formName" jdbcType="VARCHAR" property="formName"/>
        <result column="chineseDisName" jdbcType="VARCHAR" property="chineseDisName"/>
        <result column="patientId" jdbcType="VARCHAR" property="patientId"/>
        <result column="diaId" jdbcType="VARCHAR" property="diaId"/>
        <result column="recId" jdbcType="VARCHAR" property="recId"/>
        <collection  property="tRecordDiasList" ofType="com.cbkj.diagnosis.beans.business.TRecordDia">
            <result property="diaRecId" column="diaRecId" />
            <result property="questionId" column="questionId" />
            <result property="questionNumber" column="questionNumber" />
            <result property="questionName" column="questionName" />
            <result property="questionType" column="questionType" />
            <result property="optionIds" column="optionIds" />
            <result property="optionNames" column="optionNames" />
            <result property="content" column="content" />
            <result property="year" column="year" />
            <result property="month" column="month" />
            <result property="day" column="day" />
            <result property="week" column="week" />
            <result property="hour" column="hour" />
            <result property="dateUnit" column="dateUnit" />
            <result property="diaId" column="diaId" />
            <result property="questionUnit" column="questionUnit" />
            <result property="questionCode" column="questionCode" />
        </collection>

    </resultMap>


    <select id="getExcelCompletedPreQuestionnaireList"
            resultMap="BaseResultMap8"
            parameterType="com.cbkj.diagnosis.beans.business.CompletedQuestionnairePreRe">
        SELECT
            b.rec_id as recId,
        b.`create_date` as finishTime,
        tai.`USER_NAME` patientName,
        tai.`card_number` patientCardNum,
        tai.`SEX` patientSex,
        tai.`AGE` patientAge,
        c.`form_name` formName,
        c.`chinese_dis_name` chineseDisName,
        tai.`USER_ID` patientId,
        c.dia_id diaId,
        b.rec_id recId,

        a.dia_rec_id diaRecId,

        a.question_id questionId,
        a.question_name questionName ,
        a.question_number questionNumber ,
        a.question_type questionType,
        a.option_ids optionIds,
        a.option_names optionNames,
        a.content,
        a.year,
        a.month,
        a.day,
        a.week,
        a.hour,
        a.date_unit dateUnit,
        b.dia_id as diaId,
        cc.question_unit questionUnit,
        cc.question_code questionCode
        FROM
        `t_record` b
        JOIN `t_pre_diagnosis_form` c ON(c.`dia_id`=b.`dia_id` and c.dia_id = #{diaId})
        join t_admin_info as tai on(tai.USER_ID = b.patient_id)
        join  t_record_dia as a on(a.rec_id = b.rec_id)
        join  t_pre_diagnosis_question as cc on(cc.question_id = a.question_id)
        join t_pre_diagnosis_form AS ff on(ff.`dia_id` = b.`dia_id` AND ff.form_type = '1')
        <where>

            <if test="textSearch != null and textSearch != ''">
                and (tai.`USER_NAME` = #{textSearch} OR tai.`card_number` = #{textSearch})
            </if>
            <if test="startDate != null">
                AND b.`create_date` >= #{startDate}
            </if>
            <if test="endDate != null">
                AND b.`create_date` &lt; #{endDate}
            </if>
            <if test="disIdsList != null">
                AND (  SELECT COUNT(*) FROM `t_pre_diagnosis_dis_mapping` AS tpddm WHERE tpddm.`dia_id` = c.`dia_id`
                AND tpddm.`dis_id`  in (
                <foreach collection="disIdsList" item="item" separator=",">
                    #{item}
                </foreach>
                )


                ) > 0
            </if>
            <if test="userId != null and userId != ''">
                AND
                (
                SELECT COUNT(*) FROM  t_pre_diagnosis_dis_mapping AS tpddm

                WHERE c.`dia_id` = tpddm.`dia_id`  AND EXISTS  ( SELECT 1 FROM sys_admin_info_dis_mapping AS sdidm WHERE sdidm.user_id = #{userId} AND sdidm.dis_id = tpddm.`dis_id` )
                )>0
            </if>

        </where>
        order by a.question_number asc
    </select>
    <select id="getExcelCompletedQuestionnaireList"
            resultMap="BaseResultMap8"
            parameterType="com.cbkj.diagnosis.beans.business.CompletedQuestionnaireCoreRe">

(
        SELECT
        b.`create_date` as finishTime,

        b.`patient_name` patientName,
        b.`patient_idcard` patientCardNum,
        b.`patient_sex` patientSex,
        b.`patient_age` patientAge,
        c.`form_name` formName,
        <!--
        a.`task_excute_time` sendTime,
        a.`task_name` taskName,
        a.`road_execute_event_way` roadExecuteEventWay,
        a.`s_road_task_id` sRoadTaskId,
        a.task_patients_id as taskPatientsId,
        -->
        b.`patient_id` patientId,

        c.dia_id diaId,
        b.rec_id recId,

        aa.dia_rec_id diaRecId,
        aa.question_id questionId,
        aa.question_name questionName ,
        aa.question_number questionNumber ,
        aa.question_type questionType,
        aa.option_ids optionIds,
        aa.option_names optionNames,
        aa.content,
        aa.year,
        aa.month,
        aa.day,
        aa.week,
        aa.hour,
        aa.date_unit dateUnit,
        b.dia_id as diaId,
        cc.question_unit questionUnit,
        cc.question_code questionCode

        FROM
         `t_record` b
        JOIN `t_pre_diagnosis_form` c ON(c.`dia_id`=b.`dia_id` and c.form_type = '2' and c.dia_id = #{diaId})
        join  t_record_dia as aa on(aa.rec_id = b.rec_id)
        join  t_pre_diagnosis_question as cc on(cc.question_id = aa.question_id)

        <where>
            <!-- 排除掉电话  -->



                <if test="startDate != null">

                    and b.`create_date` >= #{startDate}
                </if>
                <if test="endDate != null">

                    and b.`create_date` &lt;= #{endDate}
                </if>




            <if test="formName != null">
                and   c.`form_name` = #{formName}
            </if>

            <if test="diaId != null">
                and   c.dia_id = #{diaId}
            </if>



            <if test="patientName != null">
                and   b.`patient_name` = #{patientName}
            </if>
            <if test="patientCardNum != null and patientCardNum != ''">
                and   b.`patient_idcard` = #{patientCardNum}
            </if>
            <if test="disName != null and disName !=''">
                AND (
                c.`chinese_dis_name` like CONCAT('%',trim(#{disName}),'%')
                or
                c.`chinese_dis_name` like CONCAT('%',trim(#{disName}),'%')
                )
            </if>
        </where>
        )


    </select>
    <select id="getExcelCompletedQuestionnaireYuZhenList"
            resultMap="BaseResultMap8"
            parameterType="com.cbkj.diagnosis.beans.business.CompletedQuestionnaireCoreRe">


        SELECT
        b.`create_date` as finishTime,
      <!--  a.`task_excute_time` sendTime,-->
        a.`USER_NAME` patientName,
        a.`card_number` patientCardNum,
        a.`SEX` patientSex,
        a.`AGE` patientAge,
        c.`form_name` formName,
        '预诊单' taskName,
        '1' roadExecuteEventWay,
        a.`USER_ID` patientId,
       <!-- a.`s_road_task_id` sRoadTaskId, -->
    <!--    a.task_patients_id as taskPatientsId,-->
        c.dia_id diaId,
       b.rec_id recId

from

         `t_record` b
        JOIN `t_pre_diagnosis_form` c ON(c.`dia_id`=b.`dia_id`)
            join `t_admin_info` a ON(a.USER_ID = b.patient_id)

        <where>
            <!-- 排除掉电话  -->
      <!--      a.`task_excute_status` = 8 AND a.`road_execute_event_way` != '3' -->
            <if test="dateType != null and dateType == 1">

                <if test="startDate != null">

                    and b.`create_date` >= #{startDate}
                </if>
                <if test="endDate != null">

                    and b.`create_date` &lt;= #{endDate}
                </if>


            </if>
      <!--      <if test="dateType != null and dateType == 2">


                <if test="startDate != null">

                    and a.`task_excute_time` >= #{startDate}
                </if>
                <if test="endDate != null">

                    and a.`task_excute_time` &lt;= #{endDate}
                </if>

            </if>
            <if test="formName != null">
                and   c.`form_name` = #{formName}
            </if>
-->
            <if test="diaId != null">
                and   c.dia_id = #{diaId}
            </if>
   <!--         <if test="sRoadTaskId != null">
                and  ( a.`s_road_task_id` = #{sRoadTaskId}
                <if test="taskName != null">
                    or a.`task_name` = #{taskName}
                </if>
                )
            </if>

            <if test="roadExecuteEventWay != null">
                and   a.`road_execute_event_way` = #{roadExecuteEventWay}
            </if>
            <if test="patientName != null">
                and   a.`patient_name` = #{patientName}
            </if>
            <if test="patientCardNum != null and patientCardNum != ''">
                and   a.`patient_card_number` = #{patientCardNum}
            </if>
            <if test="disName != null and disName !=''">
                AND (
                c.`chinese_dis_name` like CONCAT('%',trim(#{disName}),'%')
                or
                c.`chinese_dis_name` like CONCAT('%',trim(#{disName}),'%')
                )
            </if>
            -->
        </where>



    </select>
    <select id="getPatientListTaskByIds" resultMap="BaseResultMap" parameterType="List">
        select
        <include refid="Base_Column_List"/>
        from s_road_task_patients where task_patients_id in
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
and task_excute_status != 8 and status = '0'

    </select>

    <resultMap id="BaseResultMap88" type="com.cbkj.diagnosis.beans.business.CompletedQuestionnairePreDiagnosisListRes">
        <result column="finishTime" jdbcType="TIMESTAMP" property="finishTime"/>
        <result column="patientAge" jdbcType="VARCHAR" property="patientAge"/>
        <result column="rec_id" jdbcType="VARCHAR" property="recId"/>
    </resultMap>

    <select id="getCompletedQuestionnairePreDiagnosisList" resultMap="BaseResultMap88"
            parameterType="com.cbkj.diagnosis.beans.business.CompletedQuestionnairePreRe">

        SELECT tr.`create_date` AS finishTime,
               tr.`patient_idcard` as patientIdcard ,
               tr.`patient_name` as patientName,
               tr.`patient_age` AS patientAge
        ,tr.`patient_sex` as patientSex,
         tpd.`chinese_dis_name` as chineseDisName
             ,tr.`rec_id`,tr.`patient_id` as patientId,tpd.`dia_id` as diaId

        FROM t_record AS tr JOIN `t_pre_diagnosis_form` AS tpd ON(tpd.`dia_id` = tr.`dia_id`)



        <where>
            tpd.form_type = '1'
        <if test="textSearch != null and textSearch != ''">
            and (tr.`patient_name` = #{textSearch} OR tr.`patient_idcard` = #{textSearch})
        </if>
            <if test="startDate != null">
                AND tr.`create_date` >= #{startDate}
            </if>
            <if test="endDate != null">
                AND tr.`create_date` &lt; #{endDate}
            </if>
            <if test="disIdsList != null">
                AND (  SELECT COUNT(*) FROM `t_pre_diagnosis_dis_mapping` AS tpddm WHERE tpddm.`dia_id` = tr.`dia_id`
                AND tpddm.`dis_id`  in (
                <foreach collection="disIdsList" item="item" separator=",">
                    #{item}
                </foreach>
                    )


                                                                                   ) > 0
            </if>
            <if test="userId != null and userId != ''">
                AND
                (
                SELECT COUNT(*) FROM  t_pre_diagnosis_dis_mapping AS tpddm

                WHERE tr.`dia_id` = tpddm.`dia_id`  AND EXISTS  ( SELECT 1 FROM sys_admin_info_dis_mapping AS sdidm WHERE sdidm.user_id = #{userId} AND sdidm.dis_id = tpddm.`dis_id` )
                )>0
            </if>

        </where>
order by tr.`create_date` desc
    </select>
    <resultMap id="getCountQuestionnaireList" type="com.cbkj.diagnosis.beans.business.CompletedQuestionnairePreCount">
        <id column="diaId" jdbcType="VARCHAR"  property="diaId" />
        <id column="num" jdbcType="INTEGER"  property="num" />
        <id column="chineseDisName" jdbcType="VARCHAR"  property="chineseDisName" />

    </resultMap>

    <select id="getCountQuestionnaireList"
            resultMap="getCountQuestionnaireList" parameterType="com.cbkj.diagnosis.beans.business.CompletedQuestionnairePreRe">

        SELECT a.`dia_id` diaId ,b.`chinese_dis_name` chineseDisName ,COUNT(*) num FROM `t_record` AS a JOIN `t_pre_diagnosis_form` AS b ON(a.`dia_id` = b.`dia_id` AND b.`form_type` = '1' )
<where>
    <if test="textSearch != null and textSearch != ''">
        and (a.`patient_name` = #{textSearch} OR a.`patient_idcard` = #{textSearch})
    </if>
    <if test="startDate != null">
        AND a.`create_date` >= #{startDate}
    </if>
    <if test="endDate != null">
        AND a.`create_date` &lt; #{endDate}
    </if>
    <if test="disIds != null">
        AND (  SELECT COUNT(*) FROM `t_pre_diagnosis_dis_mapping` AS tpddm WHERE tpddm.`dia_id` = a.`dia_id`
        AND tpddm.`dis_id`  in (
        <foreach collection="disIdsList" item="item" separator=",">
            #{item}
        </foreach>
        )


        ) > 0
    </if>
    <if test="userId != null and userId != ''">
        AND
        (
        SELECT COUNT(*) FROM  t_pre_diagnosis_dis_mapping AS tpddm

        WHERE a.`dia_id` = tpddm.`dia_id`  AND EXISTS  ( SELECT 1 FROM sys_admin_info_dis_mapping AS sdidm WHERE sdidm.user_id = #{userId} AND sdidm.dis_id = tpddm.`dis_id` )
        )>0
    </if>

</where>
        GROUP BY a.`dia_id`

    </select>
    <select id="countByConditons" resultType="java.lang.Integer" parameterType="com.cbkj.diagnosis.beans.task.SRoadTaskPatients">
        select count(*) from s_road_task_patients as c
where  s_road_task_id = #{sRoadTaskId} and c.patient_id = #{patientId}
<!--
            c.`road_execute_event_content_id` = #{roadExecuteEventContentId}
                and c.road_execute_event_time = #{roadExecuteEventTime}
                and c.road_execute_event_unit = #{roadExecuteEventUnit}
                and c.records_id = #{recordsId}

                and c.road_execute_event_type = #{roadExecuteEventType} -->
        and c.status = '0' and c.task_excute_status != '3'
    </select>


    <update id="updateClosedByPatientId" parameterType="String">
        update s_road_task_patients set closed_status = '1',closed_no= #{closedNo},closed_time=now() where patient_id = #{patientId} and closed_status = '0'
    </update>

    <update id="updateClosedById" parameterType="String">
        update s_road_task_patients set closed_status = '1',closed_no= #{closedNo},closed_time=now() where task_patients_id = #{id} and closed_status = '0'
    </update>

    <update id="updateClosedByclosdNo" parameterType="String">
        update s_road_task_patients set closed_status = #{closedStatus}, closed_time=now() where closed_no = #{closedNo}
    </update>

</mapper>