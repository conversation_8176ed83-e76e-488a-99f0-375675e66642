package com.cbkj.diagnosis.common.utils.excel;


import com.cbkj.diagnosis.beans.business.CompletedQuestionnaireListExcel;
import com.cbkj.diagnosis.beans.business.TPreDiagnosisQuestion;
import com.cbkj.diagnosis.beans.business.TRecordDia;
import com.cbkj.diagnosis.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Component
public class ExportExcel<T> {

    /**
     * 创建表格标题样式
     *
     * @param workbook workbook
     * @return org.apache.poi.xssf.usermodel.XSSFCellStyle
     * <AUTHOR>
     * @date 2022/1/14
     */
    public static CellStyle createTitleCellStyle(SXSSFWorkbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        // 字体样式
        Font fontStyle = workbook.createFont();
        // 加粗
        // fontStyle.setBold(true);
        // 字体
        fontStyle.setFontName("黑体");
        // 大小
        fontStyle.setFontHeightInPoints((short) 12);
        cellStyle.setFont(fontStyle);
        // 水平居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        return cellStyle;
    }

    /**
     * 创建表格普通样式
     *
     * @param workbook workbook
     * @return org.apache.poi.xssf.usermodel.XSSFCellStyle
     * <AUTHOR>
     * @date 2022/1/14
     */
    public static CellStyle createNormalCellStyle(SXSSFWorkbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        // 字体样式
        Font fontStyle = workbook.createFont();
        // 加粗
        // fontStyle.setBold(true);
        // 字体
        fontStyle.setFontName("宋体");
        // 大小
        fontStyle.setFontHeightInPoints((short) 12);
        cellStyle.setFont(fontStyle);
        // 水平居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        return cellStyle;
    }


    /**
     * 组装标题行与数据行
     *
     * @param sheet   sheet
     * @param rowNum  从第几行开始填充
     * @param headers 标题集合 LinkedHashMap<T.field, title>
     * @param dataset 数值集合 Collection<T.field, value>
     * <AUTHOR>
     * @date 2022/1/14
     */
    public void packDataCell(SXSSFSheet sheet, int rowNum, LinkedHashMap<String, String> headers, List<CompletedQuestionnaireListExcel> dataList, List<TPreDiagnosisQuestion> tPreDiagnosisQuestionList) {
        // 固定标题列数量
        int colFixHeaderSize = headers.size();
        sheet.trackAllColumnsForAutoSizing();

        SXSSFRow row = sheet.createRow(rowNum);
        SXSSFCell cell;
        CellStyle normalCellStyle = ExportExcel.createNormalCellStyle(sheet.getWorkbook());

        //深度拷贝LinkedHashMap<String, String> headers
        LinkedHashMap<String, String> headersCopy = new LinkedHashMap<>();
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            headersCopy.put(entry.getKey(), entry.getValue());
        }
        for (int i = 0; i < tPreDiagnosisQuestionList.size(); i++) {
            TPreDiagnosisQuestion tPreDiagnosisQuestion = tPreDiagnosisQuestionList.get(i);
            headersCopy.put(tPreDiagnosisQuestion.getQuestionId() + "", tPreDiagnosisQuestion.getQuestionName());
        }

        Iterator<Map.Entry<String, String>> headerIt = headersCopy.entrySet().iterator();
        int colIndex = 0;
        while (headerIt.hasNext()) {
            cell = row.createCell(colIndex);
            cell.setCellValue(new XSSFRichTextString(headerIt.next().getValue()));
            sheet.autoSizeColumn(colIndex);

            colIndex++;
        }
        int rowIndex = rowNum + 1;
        for (int i = 0; i < dataList.size(); i++) {
            row = sheet.createRow(rowIndex);

            CompletedQuestionnaireListExcel completedQuestionnaireListExcel = dataList.get(i);
            Iterator<String> headerKeys = headers.keySet().iterator();
            colIndex = 0;

            while (headerKeys.hasNext()) {
                cell = row.createCell(colIndex);
                String fieldName = headerKeys.next();
                if (colIndex < colFixHeaderSize) {
                    if (colIndex == 0) {
                        XSSFRichTextString richString = new XSSFRichTextString((rowIndex ) + "");
                        cell.setCellValue(richString);
                        cell.setCellStyle(normalCellStyle);
                    } else {
                        //反射，动态调用getXxx()方法得到属性值
                        String getMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                        Method getMethod = null;
                        try {
                            getMethod = completedQuestionnaireListExcel.getClass().getMethod(getMethodName);
                        } catch (NoSuchMethodException e) {
                            log.error("反射获取get方法异常{}",getMethodName);
                            log.error("反射获取get方法异常,异常信息{}",e.getMessage());
//                            throw new RuntimeException(e);
                            continue;
                        }
                        Object value = null;
                        try {
                            value = getMethod.invoke(completedQuestionnaireListExcel);
                        } catch (IllegalAccessException | InvocationTargetException e) {
                            throw new RuntimeException(e);
                        }
                        String textValue = "";
                        if (value != null){
                            if (value instanceof Date){
                                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
                                simpleDateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
                                textValue = simpleDateFormat.format(value);
                            }else {
                                textValue = value.toString();
                                if (StringUtils.isNotBlank(textValue) && textValue.contains("F")){
                                    textValue= "女";
                                }else if (StringUtils.isNotBlank(textValue) && textValue.contains("M")){
                                    textValue = "男";
                                }
                            }
                        }
                        XSSFRichTextString richString = new XSSFRichTextString(textValue);
                        cell.setCellValue(richString);
                        cell.setCellStyle(normalCellStyle);
                    }

                } else {
                    //此时的fieldName 就是questionId的值
                    List<TRecordDia> tRecordDiasList = completedQuestionnaireListExcel.getTRecordDiasList();
                    int temp = 0 ;
                    for (int i1 = 0; i1 < tRecordDiasList.size(); i1++) {
                        TRecordDia tRecordDia = tRecordDiasList.get(i1);
                        if (fieldName.equals(tRecordDia.getQuestionId() + "")){
                            String s = tRecordDia.getContent().replaceAll("[\\[\\]\"]", "");
                            XSSFRichTextString richString = new XSSFRichTextString(s);
                            if (StringUtils.isNotBlank(s) && s.contains("F")){
                                cell.setCellValue("女");
                            }else if (StringUtils.isNotBlank(s) && s.contains("M")){
                                cell.setCellValue("男");
                            }else {
                                String year = tRecordDia.getYear();
                                String month = tRecordDia.getMonth();
                                String day = tRecordDia.getDay();
                                String hour = tRecordDia.getHour();
                                String week = tRecordDia.getWeek();
                                if ("4".equals(tRecordDia.getQuestionType())){
                                    //日期空间：3年，5月，4周，3日；
                                    StringBuilder stringBuilder = new StringBuilder();
                                    if (StringUtils.isNotBlank(year)){
                                        year = year + "年";
                                        stringBuilder.append(year);
                                    }
                                    if (StringUtils.isNotBlank(month)){
                                        month = month + "月";
                                        if (StringUtils.isNotBlank(stringBuilder.toString())){
                                            stringBuilder.append(",").append(month);
                                        }
                                    }
                                    if (StringUtils.isNotBlank(week)){
                                        week = week + "周";
                                        if (StringUtils.isNotBlank(stringBuilder.toString())){
                                            stringBuilder.append(",").append(week);
                                        }
                                    }
                                    if (StringUtils.isNotBlank(day)){
                                        day = day + "日";
                                        if (StringUtils.isNotBlank(stringBuilder.toString())){
                                            stringBuilder.append(",").append(day);
                                        }
                                    }
                                    if (StringUtils.isNotBlank(hour)){
                                        hour = hour + "小时";
                                        if (StringUtils.isNotBlank(stringBuilder.toString())){
                                            stringBuilder.append(",").append(hour);
                                        }
                                    }
                                    cell.setCellValue( stringBuilder.toString());
                                }else if ("6".equals(tRecordDia.getQuestionType())){
                                    StringBuilder stringBuilder = new StringBuilder();
                                    //时间控件：2024年8月5日；
                                    if (StringUtils.isNotBlank(year)){
                                        year = year + "年";
                                        stringBuilder.append(year);
                                    }
                                    if (StringUtils.isNotBlank(month)){
                                        month = month + "月";
                                        if (StringUtils.isNotBlank(stringBuilder.toString())){
                                            stringBuilder.append(month);
                                        }
                                    }
                                    if (StringUtils.isNotBlank(day)){
                                        day = day + "日";
                                        if (StringUtils.isNotBlank(stringBuilder.toString())){
                                            stringBuilder.append(day);
                                        }
                                    }
                                    cell.setCellValue(stringBuilder.toString());
                                }else{
                                    cell.setCellValue(richString);
                                }
                            }
                            cell.setCellStyle(normalCellStyle);
                            temp = 1;
                            break;
                        }
                    }
                        if (temp == 0){
                            XSSFRichTextString richString = new XSSFRichTextString(" ");
                            cell.setCellValue(richString);
                            cell.setCellStyle(normalCellStyle);
                        }

                }

                colIndex++;
            }


            rowIndex++;
        }


    }


    public void packDataCell(SXSSFSheet sheet, int rowNum, LinkedHashMap<String, String> headers, Collection<T> dataset) {
        SXSSFRow row = sheet.createRow(rowNum);
        SXSSFCell cell;
        CellStyle normalCellStyle = ExportExcel.createNormalCellStyle(sheet.getWorkbook());

        Iterator<Map.Entry<String, String>> headerIt = headers.entrySet().iterator();
        int colIndex = 0;
        while (headerIt.hasNext()) {
            cell = row.createCell(colIndex);
            cell.setCellValue(new XSSFRichTextString(headerIt.next().getValue()));
            colIndex++;
        }

        //数据行
        Iterator<T> it = dataset.iterator();
        int rowIndex = rowNum + 1;
        while (it.hasNext()) {
            row = sheet.createRow(rowIndex);
            T t = it.next();

            Iterator<String> headerKeys = headers.keySet().iterator();
            colIndex = 0;
            while (headerKeys.hasNext()) {
                try {

                    cell = row.createCell(colIndex);
                    String fieldName = headerKeys.next();
                    //反射，动态调用getXxx()方法得到属性值
                    String getMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                    Method getMethod = t.getClass().getMethod(getMethodName);
                    Object value = getMethod.invoke(t);
                    if (value instanceof List) {

                    } else if (value != null && value != "") {
                        String textValue = value.toString();
                        XSSFRichTextString richString = new XSSFRichTextString(textValue);
                        cell.setCellValue(richString);
                        cell.setCellStyle(normalCellStyle);
                    }
                    colIndex++;

                } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                    e.printStackTrace();
                }
            }
            rowIndex++;
        }

    }

    /**
     * 指定路径下生成EXCEL文件
     */
    public void getExportedFile(SXSSFWorkbook workbook, String name, HttpServletResponse response) {
        BufferedOutputStream fos = null;
        try {
            String fileName = name + "-" + DateUtil.getDateFormats(DateUtil.YYYYMMDD, null) + ".xlsx";
            response.setContentType("application/x-msdownload");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            fos = new BufferedOutputStream(response.getOutputStream());
            workbook.write(fos);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (fos != null) {
                try {
                    // 此方法能够删除导出过程中生成的xml临时文件
                    workbook.dispose();
                    fos.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    public void getExportedFilePath(XSSFWorkbook workbook, String name) {
        String fileName = name + "-" + DateUtil.getDateFormats(DateUtil.date5, null) + ".xlsx";
        //文件存储路径
        String realPath = "/app/uploadFile/cbkjFile/" + fileName;
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(realPath);
            workbook.write(fos);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


}