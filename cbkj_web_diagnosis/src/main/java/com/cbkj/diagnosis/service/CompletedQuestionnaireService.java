package com.cbkj.diagnosis.service;

import com.alibaba.fastjson.JSON;
import com.cbkj.diagnosis.beans.business.*;
import com.cbkj.diagnosis.beans.sysBeans.AdminInfo;
import com.cbkj.diagnosis.beans.sysBeans.AdminRule;
import com.cbkj.diagnosis.beans.task.SRoadTask;
import com.cbkj.diagnosis.common.utils.AdminWebUtils;
import com.cbkj.diagnosis.common.utils.Page;
import com.cbkj.diagnosis.common.utils.ZipUtils;
import com.cbkj.diagnosis.common.utils.excel.ExportExcel;
import com.cbkj.diagnosis.mapper.business.TPreDiagnosisQuestionMapper;
import com.cbkj.diagnosis.mapper.business.TRecordDiaMapper;
import com.cbkj.diagnosis.mapper.task.SRoadTaskMapper;
import com.cbkj.diagnosis.mapper.task.SRoadTaskPatientsMapper;
import com.cbkj.diagnosis.service.vo.CompletedQuestionnaireRe;
import com.github.pagehelper.PageHelper;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

@Service
@Log4j2
public class CompletedQuestionnaireService {


    private final SRoadTaskPatientsMapper sRoadTaskPatientsMapper;

    private final TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper;
    private final TRecordDiaMapper tRecordDiaMapper;

    private final SRoadTaskMapper sRoadTaskMapper;

    public CompletedQuestionnaireService(SRoadTaskPatientsMapper sRoadTaskPatientsMapper, TPreDiagnosisQuestionMapper tPreDiagnosisQuestionMapper, TRecordDiaMapper tRecordDiaMapper, SRoadTaskMapper sRoadTaskMapper) {
        this.sRoadTaskPatientsMapper = sRoadTaskPatientsMapper;
        this.tPreDiagnosisQuestionMapper = tPreDiagnosisQuestionMapper;
        this.tRecordDiaMapper = tRecordDiaMapper;
        this.sRoadTaskMapper = sRoadTaskMapper;
    }

    public Object getList(CompletedQuestionnaireRe completedQuestionnaireRe, Page page) {
        log.info("completedQuestionnaireRe入参{}", JSON.toJSONString(completedQuestionnaireRe));
        CompletedQuestionnaireCoreRe completedQuestionnaireCoreRe = new CompletedQuestionnaireCoreRe();
        if (StringUtils.isNotBlank(completedQuestionnaireRe.getSroadIdsstr())) {
            completedQuestionnaireCoreRe.setSRoadTaskIdList(completedQuestionnaireRe.getSroadIdsstr().split(","));
        }
        completedQuestionnaireCoreRe.setPage( (page.getPage()-1) * page.getLimit());
        completedQuestionnaireCoreRe.setLimit(page.getLimit());
        //PageHelper.startPage(page.getPage(), page.getLimit());

        BeanUtils.copyProperties(completedQuestionnaireRe, completedQuestionnaireCoreRe);
        if (StringUtils.isNotBlank(completedQuestionnaireCoreRe.getStartDate())) {
            completedQuestionnaireCoreRe.setStartDate(completedQuestionnaireCoreRe.getStartDate().substring(0, 10) + " 00:00:00");
        }
        if (StringUtils.isNotBlank(completedQuestionnaireCoreRe.getEndDate())) {
            completedQuestionnaireCoreRe.setEndDate(completedQuestionnaireCoreRe.getEndDate().substring(0, 10) + " 23:59:59");
        }
//        if (StringUtils.isNotBlank(completedQuestionnaireRe.getSRoadTaskId())) {
//            SRoadTask objectById = sRoadTaskMapper.getObjectById(completedQuestionnaireRe.getSRoadTaskId());
//            if (objectById != null) {
//                completedQuestionnaireCoreRe.setTaskName(objectById.getTaskName());
//            }
//        }
        //不一定能关联到任务 所以删除下main代码。主动发送的没有任务id
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        completedQuestionnaireCoreRe.setCreateUserId(currentHr.getUserId());
        List<AdminRule> roles = currentHr.getRoles();

        if (roles.stream().anyMatch(role -> role.getRoleName().contains("管理"))) {
            completedQuestionnaireCoreRe.setCreateUserId(null);
        }

        List<CompletedQuestionnaireListRes> completedQuestionnaireListResList =
                sRoadTaskPatientsMapper.getCompletedQuestionnaireList(completedQuestionnaireCoreRe);
        int totalNum = sRoadTaskPatientsMapper.getCountNumCompletedQuestionnaireList(completedQuestionnaireCoreRe);
        //PageHelper.clearPage();

        return Page.getLayuiData(true, "SUCCESS", totalNum, totalNum > page.getLimit() * page.getPage(), completedQuestionnaireListResList);

//        return Page.getLayUiTablePageData(completedQuestionnaireListResList);
    }


    public void downloadQuestionnaire(CompletedQuestionnaireRe questionnaireRequest, HttpServletResponse response) {
        CompletedQuestionnaireCoreRe completedQuestionnaireCoreRe = new CompletedQuestionnaireCoreRe();
        BeanUtils.copyProperties(questionnaireRequest, completedQuestionnaireCoreRe);
        //Excel标题
        //String fileName = "中草药处方明细数据统计";
        if (StringUtils.isNotBlank(completedQuestionnaireCoreRe.getStartDate())) {
            completedQuestionnaireCoreRe.setStartDate(completedQuestionnaireCoreRe.getStartDate().substring(0, 10) + " 00:00:00");
            if (StringUtils.isNotBlank(completedQuestionnaireCoreRe.getEndDate())) {
                completedQuestionnaireCoreRe.setEndDate(completedQuestionnaireCoreRe.getEndDate().substring(0, 10) + " 23:59:59");
            }
        }
        if (StringUtils.isNotBlank(questionnaireRequest.getSroadIdsstr())) {
            SRoadTask objectById = sRoadTaskMapper.getObjectById(questionnaireRequest.getSroadIdsstr());
            if (objectById != null) {
                completedQuestionnaireCoreRe.setTaskName(objectById.getTaskName());
            }
        }
        AdminInfo currentHr = AdminWebUtils.getCurrentHr();
        if (currentHr == null) {
            log.error("下载随访原始问卷：当前鉴权用户过期");
            return;
        }
        completedQuestionnaireCoreRe.setCreateUserId(currentHr.getUserId());
        List<AdminRule> roles = currentHr.getRoles();

        if (roles.stream().anyMatch(role -> role.getRoleName().contains("管理"))) {
            completedQuestionnaireCoreRe.setCreateUserId(null);
        }
        List<CompletedQuestionnaireCount> countCompletedQuestionnaireList = sRoadTaskPatientsMapper.getCountCompletedQuestionnaireList(completedQuestionnaireCoreRe);
        ArrayList<byte[]> bytes = new ArrayList<byte[]>();
        StringBuilder fNames = new StringBuilder();
        int maxCount = 1000;
        for (int i = 0; i < countCompletedQuestionnaireList.size(); i++) {
            // 声明一个工作薄
            SXSSFWorkbook workbook = new SXSSFWorkbook(100);
            CompletedQuestionnaireCount completedQuestionnaireCount = countCompletedQuestionnaireList.get(i);
            Integer totalCount = completedQuestionnaireCount.getNum();
            String diaId = completedQuestionnaireCount.getDiaId();
//            fNames.append("-"+i).append(",");
            fNames.append(completedQuestionnaireCount.getFormName()).append("-").append(diaId).append(",");
            LinkedHashMap<String, String> headers = new LinkedHashMap<>();
            headers.put("id", "序号");
            headers.put("finishTime", "完成时间");
            headers.put("sendTime", "发送时间");
            headers.put("patientName", "姓名");
            headers.put("patientSex", "性别");
            headers.put("patientCardNum", "身份证");
            headers.put("formName", "随访内容");
            headers.put("patientAge", "年龄");
            ExportExcel<Object> exportExcel = new ExportExcel<>();
            //如果导出数据量大于   设定的最大数据量    则最多不能超过设定的数量
            if (totalCount > maxCount) {
                int number = (totalCount % maxCount) == 0 ? totalCount
                        / maxCount : totalCount / maxCount + 1;
                for (int i2 = 0; i2 < number; i2++) {
                    PageHelper.startPage(i2 + 1, maxCount);
                    completedQuestionnaireCoreRe.setDiaId(diaId);
                    //重新赋值，因为加密了。
//                    completedQuestionnaireCoreRe.setPatientName(questionnaireRequest.getPatientName());
//                    completedQuestionnaireCoreRe.setPatientCardNum(questionnaireRequest.getPatientCardNum());
                    //取当前问卷id的所有患者信息
                    List<CompletedQuestionnaireListExcel> list = sRoadTaskPatientsMapper.getExcelCompletedQuestionnaireList(completedQuestionnaireCoreRe);
                    PageHelper.clearPage();
                    //获取这个问卷的完整的问题
                    List<TPreDiagnosisQuestion> tPreDiagnosisQuestionList = tPreDiagnosisQuestionMapper.getAllQuestionListByDiaId(diaId);
                    //把患者的这个问卷的所有问题的答题找出来。
//                    for (CompletedQuestionnaireListExcel completedQuestionnaireListExcel : list) {
//                        completedQuestionnaireListExcel.setTRecordDiasList(tRecordDiaMapper.getObjectByRecId(completedQuestionnaireListExcel.getRecId()));
//                    }

                    // 生成一个表格
                    SXSSFSheet sheet = workbook.createSheet();
                    //createSheet(sheet,workbook,fileName,prescriptionStatisticsVO);
                    exportExcel.packDataCell(sheet, 0, headers, list, tPreDiagnosisQuestionList);
                    try {
                        //每创建完成一个sheet页就把数据刷新到磁盘
                        sheet.flushRows();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }

                }
                try {
                    byte[] excelBytes = convertToByteArray(workbook);
                    //  excelBytes 中包含了 SXSSFWorkbook 对象的内容
                    // 将 excelBytes 保存到文件、发送给客户端等
                    bytes.add(excelBytes);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                } finally {
                    //需要关闭 workbook
                    workbook.dispose();
                }

            } else {
                completedQuestionnaireCoreRe.setDiaId(diaId);
                //重新赋值，因为加密了。
//                completedQuestionnaireCoreRe.setPatientName(questionnaireRequest.getPatientName());
//                completedQuestionnaireCoreRe.setPatientCardNum(questionnaireRequest.getPatientCardNum());
                //取当前问卷id的所有患者信息
                List<CompletedQuestionnaireListExcel> list = sRoadTaskPatientsMapper.getExcelCompletedQuestionnaireList(completedQuestionnaireCoreRe);
                //获取这个问卷的完整的问题
                List<TPreDiagnosisQuestion> tPreDiagnosisQuestionList = tPreDiagnosisQuestionMapper.getAllQuestionListByDiaId(diaId);
                //把患者的这个问卷的所有问题的答题找出来。
//                for (CompletedQuestionnaireListExcel completedQuestionnaireListExcel : list) {
//                    completedQuestionnaireListExcel.setTRecordDiasList(tRecordDiaMapper.getObjectByRecId(completedQuestionnaireListExcel.getRecId()));
//                }
                // 生成一个表格
                SXSSFSheet sheet = workbook.createSheet();
                exportExcel.packDataCell(sheet, 0, headers, list, tPreDiagnosisQuestionList);

                try {
                    byte[] excelBytes = convertToByteArray(workbook);
                    //  excelBytes 中包含了 SXSSFWorkbook 对象的内容
                    // 将 excelBytes 保存到文件、发送给客户端等
                    bytes.add(excelBytes);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                } finally {
                    //需要关闭 workbook
                    workbook.dispose();
                }
                //exportExcel.getExportedFile(workbook, fileName, response);

            }
        }


        //打包输出。
        ZipUtils.exposeZip("随访问卷/量表原始数据", response, bytes, fNames.toString().split(","));

    }

    public static byte[] convertToByteArray(SXSSFWorkbook workbook) throws IOException {

        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            workbook.write(byteArrayOutputStream);
            return byteArrayOutputStream.toByteArray();
        }
    }


    public void createSheet(SXSSFSheet sheet, SXSSFWorkbook workbook, String fileName) {
        // 设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 20);

        //标题样式
        CellStyle titleCellStyle = ExportExcel.createTitleCellStyle(workbook);
        //普通样式
        CellStyle normalCellStyle = ExportExcel.createNormalCellStyle(workbook);

        //标题行
        SXSSFRow row = sheet.createRow(0);
        SXSSFCell cell1 = row.createCell(0);
        cell1.setCellValue(new XSSFRichTextString(fileName));
        cell1.setCellStyle(titleCellStyle);

        // 合并单元格
        CellRangeAddress cra = new CellRangeAddress(0, 0, 0, 18);
        sheet.addMergedRegion(cra);

        //第2行
        SXSSFRow row2 = sheet.createRow(1);
        SXSSFCell cell20 = row2.createCell(0);
//        cell20.setCellValue(new XSSFRichTextString("统计时间：" + prescriptionStatisticsVO.getPreTimeStart() + " ~ " + prescriptionStatisticsVO.getPreTimeEnd()));
        cell20.setCellStyle(normalCellStyle);

        //合并单元格
        CellRangeAddress cra21 = new CellRangeAddress(1, 1, 0, 18);
        sheet.addMergedRegion(cra21);
    }


}
